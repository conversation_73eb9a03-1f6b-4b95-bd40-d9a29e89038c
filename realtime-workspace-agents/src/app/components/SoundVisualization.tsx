"use client";

import React, { useEffect, useRef, useState } from "react";

interface SoundVisualizationProps {
  audioElement: HTMLAudioElement | null;
  isVisible: boolean;
  className?: string;
}

const SoundVisualization: React.FC<SoundVisualizationProps> = ({
  audioElement,
  isVisible,
  className = "",
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize audio analysis
  useEffect(() => {
    if (!audioElement || !isVisible) {
      cleanup();
      return;
    }

    const initializeAudioAnalysis = () => {
      try {
        // Check if audio element has a stream
        const stream = audioElement.srcObject as MediaStream;
        if (!stream) {
          console.log("No audio stream available yet");
          return;
        }

        // Create audio context if it doesn't exist
        if (!audioContextRef.current) {
          audioContextRef.current = new AudioContext();
        }

        const audioContext = audioContextRef.current;

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }

        // Create analyser node
        const analyser = audioContext.createAnalyser();
        analyser.fftSize = 256;
        analyser.smoothingTimeConstant = 0.8;

        // Create source from stream
        const source = audioContext.createMediaStreamSource(stream);
        source.connect(analyser);

        // Create data array for frequency data
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        analyserRef.current = analyser;
        dataArrayRef.current = dataArray;
        sourceRef.current = source;

        setIsInitialized(true);
        startVisualization();
      } catch (error) {
        console.error("Error initializing audio analysis:", error);
      }
    };

    // Try to initialize immediately
    initializeAudioAnalysis();

    // Also listen for when the audio element gets a stream
    const handleLoadedMetadata = () => {
      initializeAudioAnalysis();
    };

    audioElement.addEventListener("loadedmetadata", handleLoadedMetadata);

    return () => {
      audioElement.removeEventListener("loadedmetadata", handleLoadedMetadata);
      cleanup();
    };
  }, [audioElement, isVisible]);

  const cleanup = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    if (sourceRef.current) {
      try {
        sourceRef.current.disconnect();
      } catch (e) {
        // Ignore disconnect errors
      }
      sourceRef.current = null;
    }

    if (audioContextRef.current && audioContextRef.current.state !== "closed") {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    analyserRef.current = null;
    dataArrayRef.current = null;
    setIsInitialized(false);
  };

  const startVisualization = () => {
    if (!canvasRef.current || !analyserRef.current || !dataArrayRef.current) {
      return;
    }

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const analyser = analyserRef.current;
    const dataArray = dataArrayRef.current;

    const draw = () => {
      if (!isVisible || !analyser || !dataArray) {
        return;
      }

      animationRef.current = requestAnimationFrame(draw);

      // Get frequency data
      analyser.getByteFrequencyData(dataArray);

      // Clear canvas
      ctx.fillStyle = "rgb(15, 23, 42)"; // slate-900 background
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Calculate average volume for overall amplitude
      const average =
        dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedAverage = average / 255;

      // Draw frequency bars
      const barWidth = canvas.width / dataArray.length;
      let x = 0;

      for (let i = 0; i < dataArray.length; i++) {
        const barHeight = (dataArray[i] / 255) * canvas.height * 0.8;

        // Create gradient based on frequency and amplitude
        const gradient = ctx.createLinearGradient(
          0,
          canvas.height,
          0,
          canvas.height - barHeight
        );

        if (barHeight > canvas.height * 0.1) {
          // Active frequencies - use blue to cyan gradient
          gradient.addColorStop(0, "rgb(59, 130, 246)"); // blue-500
          gradient.addColorStop(0.5, "rgb(14, 165, 233)"); // sky-500
          gradient.addColorStop(1, "rgb(6, 182, 212)"); // cyan-500
        } else {
          // Low frequencies - use muted colors
          gradient.addColorStop(0, "rgb(71, 85, 105)"); // slate-600
          gradient.addColorStop(1, "rgb(100, 116, 139)"); // slate-500
        }

        ctx.fillStyle = gradient;
        ctx.fillRect(x, canvas.height - barHeight, barWidth - 1, barHeight);

        x += barWidth;
      }

      // Draw central waveform visualization
      if (normalizedAverage > 0.05) {
        const centerY = canvas.height / 2;
        const waveHeight = normalizedAverage * 60;

        ctx.strokeStyle = "rgb(34, 197, 94)"; // green-500
        ctx.lineWidth = 3;
        ctx.globalAlpha = 0.8;

        ctx.beginPath();
        for (let i = 0; i < canvas.width; i += 4) {
          const wave = Math.sin(i * 0.02 + Date.now() * 0.005) * waveHeight;
          if (i === 0) {
            ctx.moveTo(i, centerY + wave);
          } else {
            ctx.lineTo(i, centerY + wave);
          }
        }
        ctx.stroke();
        ctx.globalAlpha = 1;
      }

      // Draw volume indicator
      const volumeBarWidth = 4;
      const volumeBarHeight = normalizedAverage * canvas.height * 0.9;
      const volumeGradient = ctx.createLinearGradient(0, canvas.height, 0, 0);
      volumeGradient.addColorStop(0, "rgb(34, 197, 94)"); // green-500
      volumeGradient.addColorStop(0.7, "rgb(234, 179, 8)"); // yellow-500
      volumeGradient.addColorStop(1, "rgb(239, 68, 68)"); // red-500

      ctx.fillStyle = volumeGradient;
      ctx.fillRect(
        canvas.width - volumeBarWidth - 10,
        canvas.height - volumeBarHeight,
        volumeBarWidth,
        volumeBarHeight
      );
    };

    draw();
  };

  // Handle canvas resize
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * window.devicePixelRatio;
      canvas.height = rect.height * window.devicePixelRatio;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
      }
    };

    resizeCanvas();
    window.addEventListener("resize", resizeCanvas);

    return () => {
      window.removeEventListener("resize", resizeCanvas);
    };
  }, []);

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-full rounded-lg border border-slate-700"
        style={{ width: "100%", height: "200px" }}
      />
      {!isInitialized && (
        <div className="absolute inset-0 flex items-center justify-center bg-slate-900/50 rounded-lg">
          <div className="text-slate-400 text-sm">
            Waiting for audio stream...
          </div>
        </div>
      )}
      {isInitialized && (
        <div className="absolute top-2 left-2 text-xs text-slate-400 bg-slate-900/70 px-2 py-1 rounded">
          Agent Audio Visualization
        </div>
      )}
    </div>
  );
};

export default SoundVisualization;
